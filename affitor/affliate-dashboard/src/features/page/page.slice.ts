import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "@/store";
import type { YooptaContentValue, Yoop<PERSON>Block, CustomYooptaBlockType } from "@/types/yoopta-editor";

export interface IPage {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  content?: YooptaContentValue; // JSON content from Yoopta Editor
  content_html?: string; // HTML representation for SEO
  content_plain?: string; // Plain text for search
  excerpt?: string;
  featured_image?: any;
  meta_title?: string;
  meta_description?: string;
  status: 'draft' | 'published' | 'archived';
  view_count: number;
  author: any;
  tags?: string[];
  last_edited_at?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  // Referrer-link relationship
  referrer_link?: {
    id: number;
    documentId: string;
    name: string;
    url: string;
    short_link?: string;
    total_views?: number;
    direct_page_views?: number;
    referrer_link_views?: number;
    short_link_views?: number;
    referrer_sources?: Record<string, number>;
    referrer?: {
      id: number;
      referral_code: string;
    };
  };
}

export interface ICreatePageData {
  title: string;
  content?: YooptaContentValue;
  content_html?: string;
  content_plain?: string;
  excerpt?: string;
  featured_image?: number;
  meta_title?: string;
  meta_description?: string;
  tags?: string[];
}

export interface IUpdatePageData {
  title?: string;
  content?: YooptaContentValue;
  content_html?: string;
  content_plain?: string;
  excerpt?: string;
  featured_image?: number;
  meta_title?: string;
  meta_description?: string;
  status?: 'draft' | 'published' | 'archived';
  tags?: string[];
}

// Helper function to deep compare content objects
const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;
  if (obj1 == null || obj2 == null) return obj1 === obj2;
  if (typeof obj1 !== typeof obj2) return false;
  if (typeof obj1 !== 'object') return obj1 === obj2;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

// Helper function to check if there are unsaved changes
const hasChanges = (currentPage: IPage | null, originalData: { title: string; content: any } | null): boolean => {
  if (!currentPage || !originalData) return false;

  const titleChanged = (currentPage.title || '') !== originalData.title;
  const contentChanged = !deepEqual(currentPage.content, originalData.content);

  return titleChanged || contentChanged;
};

interface PageState {
  // Current page being edited
  currentPage: IPage | null;

  // Original page data (for comparison to detect changes)
  originalPageData: { title: string; content: any } | null;

  // List of user's pages
  pages: IPage[];

  // Available pages for referrer-link (pages not linked to any referrer-link)
  availablePagesForReferrerLink: IPage[];

  // Loading states
  loading: boolean;
  availablePagesLoading: boolean;
  createLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;
  autoSaveLoading: boolean;
  publishLoading: boolean;
  imageUploadLoading: boolean;

  // Error states
  error: string | null;
  createError: string | null;
  availablePagesError: string | null;
  updateError: string | null;
  deleteError: string | null;
  autoSaveError: string | null;
  publishError: string | null;
  imageUploadError: string | null;

  // Success messages
  successMessage: string | null;

  // Pagination
  pagination: {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
  } | null;

  // Auto-save status
  lastAutoSave: string | null;
  hasUnsavedChanges: boolean;

  // Referrer link tracking state
  referrerLinkData: {
    referrerCode?: string;
    referrerUrl?: string;
    hasTrackedView?: boolean;
  } | null;
}

const initialState: PageState = {
  currentPage: null,
  originalPageData: null,
  pages: [],
  availablePagesForReferrerLink: [],
  loading: false,
  availablePagesLoading: false,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  autoSaveLoading: false,
  publishLoading: false,
  imageUploadLoading: false,
  error: null,
  createError: null,
  updateError: null,
  availablePagesError: null,
  deleteError: null,
  autoSaveError: null,
  publishError: null,
  imageUploadError: null,
  successMessage: null,
  pagination: null,
  lastAutoSave: null,
  hasUnsavedChanges: false,
  referrerLinkData: null,
};

const pageSlice = createSlice({
  name: "page",
  initialState,
  reducers: {
    // Fetch pages
    fetchPagesRequest: (state, action: PayloadAction<{ page?: number; pageSize?: number; filters?: any }>) => {
      state.loading = true;
      state.error = null;
    },
    fetchPagesSuccess: (state, action: PayloadAction<{ pages: IPage[]; pagination: any }>) => {
      state.loading = false;
      state.pages = action.payload.pages;
      state.pagination = action.payload.pagination;
      state.error = null;
    },
    fetchPagesFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch available pages for referrer-link
    fetchAvailablePagesForReferrerLinkRequest: (state) => {
      state.availablePagesLoading = true;
      state.availablePagesError = null;
    },
    fetchAvailablePagesForReferrerLinkSuccess: (state, action: PayloadAction<IPage[]>) => {
      state.availablePagesLoading = false;
      state.availablePagesForReferrerLink = action.payload;
      state.availablePagesError = null;
    },
    fetchAvailablePagesForReferrerLinkFailure: (state, action: PayloadAction<string>) => {
      state.availablePagesLoading = false;
      state.availablePagesError = action.payload;
    },

    // Track page view
    trackPageViewRequest: (state, action: PayloadAction<{ pageId: string; referrer?: string; sourceType?: string }>) => {
      // No loading state needed for tracking as it's a background operation
    },
    trackPageViewSuccess: (state, action: PayloadAction<any>) => {
      // Optionally update view count in current page if it matches
      if (state.currentPage && state.currentPage.documentId === action.payload.pageId) {
        state.currentPage.view_count = (state.currentPage.view_count || 0) + 1;
      }

      // Mark that view tracking has been completed for this session
      if (state.referrerLinkData) {
        state.referrerLinkData.hasTrackedView = true;
      }
    },
    trackPageViewFailure: (state, action: PayloadAction<string>) => {
      // Silent failure for tracking - don't show errors to user
      console.error('Page view tracking failed:', action.payload);
    },

    // Fetch single page
    fetchPageRequest: (state, action: PayloadAction<string>) => {
      state.loading = true;
      state.error = null;
      // Clear current page data to prevent stale data during loading
      state.currentPage = null;
      state.originalPageData = null;
      state.hasUnsavedChanges = false;
    },
    fetchPageSuccess: (state, action: PayloadAction<IPage>) => {
      state.loading = false;
      state.currentPage = action.payload;
      // Store original data for comparison
      state.originalPageData = {
        title: action.payload.title || '',
        content: action.payload.content
      };
      state.error = null;
      state.hasUnsavedChanges = false;

      // Store referrer link data if page has a linked referrer_link
      if (action.payload.referrer_link) {
        state.referrerLinkData = {
          referrerCode: action.payload.referrer_link.referrer?.referral_code,
          referrerUrl: action.payload.referrer_link.url,
          hasTrackedView: false
        };
      } else {
        state.referrerLinkData = null;
      }
    },
    fetchPageFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Create page
    createPageRequest: (state, action: PayloadAction<ICreatePageData>) => {
      state.createLoading = true;
      state.createError = null;
    },
    createPageSuccess: (state, action: PayloadAction<IPage>) => {
      state.createLoading = false;
      state.currentPage = action.payload;
      state.pages.unshift(action.payload);
      state.createError = null;
      state.successMessage = "Page created successfully";
      state.hasUnsavedChanges = false;
    },
    createPageFailure: (state, action: PayloadAction<string>) => {
      state.createLoading = false;
      state.createError = action.payload;
    },

    // Update page
    updatePageRequest: (state, action: PayloadAction<{ id: string; data: IUpdatePageData }>) => {
      state.updateLoading = true;
      state.updateError = null;
    },
    updatePageSuccess: (state, action: PayloadAction<IPage>) => {
      state.updateLoading = false;
      state.currentPage = action.payload;

      // Update original data to reflect the saved state
      state.originalPageData = {
        title: action.payload.title || '',
        content: action.payload.content
      };

      // Update in pages list
      const index = state.pages.findIndex(page => page.documentId === action.payload.documentId);
      if (index !== -1) {
        state.pages[index] = action.payload;
      }

      state.updateError = null;
      state.successMessage = "Page updated successfully";
      state.hasUnsavedChanges = false;
    },
    updatePageFailure: (state, action: PayloadAction<string>) => {
      state.updateLoading = false;
      state.updateError = action.payload;
    },

    // Auto-save page
    autoSavePageRequest: (state, action: PayloadAction<{ id: string; content: any }>) => {
      state.autoSaveLoading = true;
      state.autoSaveError = null;
    },
    autoSavePageSuccess: (state, action: PayloadAction<IPage>) => {
      state.autoSaveLoading = false;
      state.currentPage = action.payload;

      // Update original data to reflect the auto-saved state
      state.originalPageData = {
        title: action.payload.title || '',
        content: action.payload.content
      };

      state.autoSaveError = null;
      state.lastAutoSave = new Date().toISOString();
      state.hasUnsavedChanges = false;
    },
    autoSavePageFailure: (state, action: PayloadAction<string>) => {
      state.autoSaveLoading = false;
      state.autoSaveError = action.payload;
    },

    // Publish page
    publishPageRequest: (state, action: PayloadAction<string>) => {
      state.publishLoading = true;
      state.publishError = null;
    },
    publishPageSuccess: (state, action: PayloadAction<IPage>) => {
      console.log('📝 [Redux] publishPageSuccess called with payload:', {
        id: action.payload.id,
        documentId: action.payload.documentId,
        title: action.payload.title,
        status: action.payload.status,
        previousStatus: state.currentPage?.status,
      });

      state.publishLoading = false;
      state.currentPage = action.payload;

      // Update original data to reflect the published state
      state.originalPageData = {
        title: action.payload.title || '',
        content: action.payload.content
      };

      // Update in pages list
      const index = state.pages.findIndex(page => page.documentId === action.payload.documentId);
      if (index !== -1) {
        state.pages[index] = action.payload;
      }

      state.publishError = null;
      state.successMessage = "Page published successfully";
      state.hasUnsavedChanges = false;

      console.log('📝 [Redux] publishPageSuccess completed, new status:', state.currentPage?.status);
    },
    publishPageFailure: (state, action: PayloadAction<string>) => {
      state.publishLoading = false;
      state.publishError = action.payload;
    },

    // Delete page
    deletePageRequest: (state, action: PayloadAction<string>) => {
      state.deleteLoading = true;
      state.deleteError = null;
    },
    deletePageSuccess: (state, action: PayloadAction<string>) => {
      state.deleteLoading = false;
      state.pages = state.pages.filter(page => page.documentId !== action.payload);
      state.deleteError = null;
      state.successMessage = "Page deleted successfully";
      
      // Clear current page if it was deleted
      if (state.currentPage?.documentId === action.payload) {
        state.currentPage = null;
      }
    },
    deletePageFailure: (state, action: PayloadAction<string>) => {
      state.deleteLoading = false;
      state.deleteError = action.payload;
    },

    // Utility actions
    clearCurrentPage: (state) => {
      state.currentPage = null;
      state.hasUnsavedChanges = false;
    },
    clearErrors: (state) => {
      state.error = null;
      state.createError = null;
      state.updateError = null;
      state.deleteError = null;
      state.autoSaveError = null;
      state.publishError = null;
      state.availablePagesError = null;
      state.imageUploadError = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    setUnsavedChanges: (state, action: PayloadAction<boolean>) => {
      // If explicitly setting to false, respect that
      // If setting to true, double-check with our comparison logic
      if (action.payload === false) {
        state.hasUnsavedChanges = false;
      } else {
        // Verify there are actual changes before setting to true
        state.hasUnsavedChanges = hasChanges(state.currentPage, state.originalPageData);
      }
    },
    updateCurrentPageContent: (state, action: PayloadAction<any>) => {
      if (state.currentPage) {
        state.currentPage.content = action.payload;
        // Check if there are actual changes compared to original
        state.hasUnsavedChanges = hasChanges(state.currentPage, state.originalPageData);
      }
    },
    updateCurrentPageTitle: (state, action: PayloadAction<string>) => {
      if (state.currentPage) {
        state.currentPage.title = action.payload;
        // Check if there are actual changes compared to original
        state.hasUnsavedChanges = hasChanges(state.currentPage, state.originalPageData);
      }
    },

    // Image upload actions
    uploadImageRequest: (state, action: PayloadAction<{ file: File; pageId: string }>) => {
      state.imageUploadLoading = true;
      state.imageUploadError = null;
    },
    uploadImageSuccess: (state, action: PayloadAction<{ s3Url: string; fileName: string; fileKey: string }>) => {
      state.imageUploadLoading = false;
      state.imageUploadError = null;
    },
    uploadImageFailure: (state, action: PayloadAction<string>) => {
      state.imageUploadLoading = false;
      state.imageUploadError = action.payload;
    },
  },
});

export const { actions } = pageSlice;

// Selectors
export const selectPageData = (state: RootState) => state.page.pages;
export const selectCurrentPage = (state: RootState) => state.page.currentPage;
export const selectPageLoading = (state: RootState) => state.page.loading;
export const selectCreatePageLoading = (state: RootState) => state.page.createLoading;
export const selectUpdatePageLoading = (state: RootState) => state.page.updateLoading;
export const selectDeletePageLoading = (state: RootState) => state.page.deleteLoading;
export const selectAutoSaveLoading = (state: RootState) => state.page.autoSaveLoading;
export const selectPublishLoading = (state: RootState) => state.page.publishLoading;
export const selectPageError = (state: RootState) => state.page.error;
export const selectCreatePageError = (state: RootState) => state.page.createError;
export const selectUpdatePageError = (state: RootState) => state.page.updateError;
export const selectDeletePageError = (state: RootState) => state.page.deleteError;
export const selectAutoSaveError = (state: RootState) => state.page.autoSaveError;
export const selectPublishError = (state: RootState) => state.page.publishError;
export const selectPageSuccessMessage = (state: RootState) => state.page.successMessage;
export const selectPagePagination = (state: RootState) => state.page.pagination;
export const selectLastAutoSave = (state: RootState) => state.page.lastAutoSave;
export const selectHasUnsavedChanges = (state: RootState) => state.page.hasUnsavedChanges;
export const selectAvailablePagesForReferrerLink = (state: RootState) => state.page.availablePagesForReferrerLink;
export const selectAvailablePagesLoading = (state: RootState) => state.page.availablePagesLoading;
export const selectAvailablePagesError = (state: RootState) => state.page.availablePagesError;
export const selectImageUploadLoading = (state: RootState) => state.page.imageUploadLoading;
export const selectImageUploadError = (state: RootState) => state.page.imageUploadError;
export const selectReferrerLinkData = (state: RootState) => state.page.referrerLinkData;

export default pageSlice.reducer;
