/**
 * page controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::page.page', ({ strapi }) => ({
  async create(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to create a page');
      }

      const { data } = ctx.request.body;

      // Validate that title is provided (required for slug generation)
      if (!data.title) {
        return ctx.badRequest('Title is required to create a page');
      }

      // Generate a unique slug from the title
      const slug = await strapi.service('api::page.page').generateUniqueSlug(data.title);

      // Ensure the author is set to the current user and include the generated slug
      const pageData = {
        ...data,
        slug,
        author: user.id,
        last_edited_at: new Date().toISOString(),
      };

      // Create the page using Document Service API
      const page = await strapi.documents('api::page.page').create({
        data: pageData,
        status: 'draft', // Always start as draft
      });

      return ctx.send({ data: page });
    } catch (err) {
      console.error('Error in create page controller:', err);
      return ctx.badRequest('Failed to create page', { error: err });
    }
  },

  async update(ctx) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;
      const { data } = ctx.request.body;

      if (!user) {
        return ctx.unauthorized('You must be logged in to update a page');
      }

      // Check if the page exists and belongs to the user
      const existingPage = await strapi.documents('api::page.page').findOne({
        documentId: id,
        populate: ['author'],
      });

      if (!existingPage) {
        return ctx.notFound('Page not found');
      }

      if (existingPage.author.id !== user.id) {
        return ctx.forbidden('You can only edit your own pages');
      }

      // Update the page with new data
      const updatedData = {
        ...data,
        last_edited_at: new Date().toISOString(),
      };

      const updatedPage = await strapi.documents('api::page.page').update({
        documentId: id,
        data: updatedData,
      });

      return ctx.send({ data: updatedPage });
    } catch (err) {
      console.error('Error in update page controller:', err);
      return ctx.badRequest('Failed to update page', { error: err });
    }
  },

  async findOne(ctx) {
    try {
      const { id } = ctx.params;
      const { user } = ctx.state;

      // Find the page with referrer_link relationship populated (including referrer)
      const page = await strapi.documents('api::page.page').findOne({
        documentId: id,
        populate: ['author', 'featured_image', 'referrer_link.referrer'],
      });

      if (!page) {
        return ctx.notFound('Page not found');
      }

      // Check if user has access (owner or published page)
      const isOwner = user && page.author.id === user.id;
      const isPublished = page.status === 'published';

      if (!isOwner && !isPublished) {
        return ctx.forbidden('Access denied');
      }

      // Increment view count if not the owner viewing their own page
      if (!isOwner && isPublished) {
        await strapi.documents('api::page.page').update({
          documentId: id,
          data: { view_count: (page.view_count || 0) + 1 },
        });
        page.view_count = (page.view_count || 0) + 1;
      }

      return ctx.send({ data: page });
    } catch (err) {
      console.error('Error in findOne page controller:', err);
      return ctx.badRequest('Failed to fetch page', { error: err });
    }
  },

  async find(ctx) {
    try {
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized('You must be logged in to view pages');
      }

      // Extract pagination parameters from query
      const page = parseInt(ctx.query.page as string) || 1;
      const pageSize = parseInt(ctx.query.pageSize as string) || 25;
      const start = (page - 1) * pageSize;

      // Get user's pages with pagination
      const pages = await strapi.documents('api::page.page').findMany({
        filters: { author: user.id },
        populate: ['featured_image'],
        sort: { createdAt: 'desc' },
        start: start,
        limit: pageSize,
      });

      return ctx.send({
        data: pages,
        meta: {
          pagination: {
            page: page,
            pageSize: pageSize,
            total: pages.length,
            pageCount: Math.ceil(pages.length / pageSize),
          }
        }
      });
    } catch (err) {
      console.error('Error in find pages controller:', err);
      return ctx.badRequest('Failed to fetch pages', { error: err });
    }
  },

  async delete(ctx) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;

      if (!user) {
        return ctx.unauthorized('You must be logged in to delete a page');
      }

      // Check if the page exists and belongs to the user
      const existingPage = await strapi.documents('api::page.page').findOne({
        documentId: id,
        populate: ['author'],
      });

      if (!existingPage) {
        return ctx.notFound('Page not found');
      }

      if (existingPage.author.id !== user.id) {
        return ctx.forbidden('You can only delete your own pages');
      }

      // Delete the page
      await strapi.documents('api::page.page').delete({
        documentId: id,
      });

      return ctx.send({ data: { id, deleted: true } });
    } catch (err) {
      console.error('Error in delete page controller:', err);
      return ctx.badRequest('Failed to delete page', { error: err });
    }
  },

  // Custom controller methods for additional routes
  async autoSave(ctx) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;
      const { content } = ctx.request.body;

      if (!user) {
        return ctx.unauthorized('You must be logged in to auto-save a page');
      }

      const updatedPage = await strapi.service('api::page.page').autoSavePage(id, user.id, content);

      return ctx.send({ data: updatedPage, message: 'Page auto-saved successfully' });
    } catch (err) {
      console.error('Error in auto-save page controller:', err);
      return ctx.badRequest('Failed to auto-save page', { error: err.message });
    }
  },

  async publish(ctx) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;

      if (!user) {
        return ctx.unauthorized('You must be logged in to publish a page');
      }

      const publishedPage = await strapi.service('api::page.page').publishPage(id, user.id);

      return ctx.send({ data: publishedPage, message: 'Page published successfully' });
    } catch (err) {
      console.error('Error in publish page controller:', err);
      return ctx.badRequest('Failed to publish page', { error: err.message });
    }
  },

  async duplicate(ctx) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;

      if (!user) {
        return ctx.unauthorized('You must be logged in to duplicate a page');
      }

      const duplicatedPage = await strapi.service('api::page.page').duplicatePage(id, user.id);

      return ctx.send({ data: duplicatedPage, message: 'Page duplicated successfully' });
    } catch (err) {
      console.error('Error in duplicate page controller:', err);
      return ctx.badRequest('Failed to duplicate page', { error: err.message });
    }
  },

  async findBySlug(ctx) {
    try {
      const { slug } = ctx.params;

      const page = await strapi.service('api::page.page').getPageBySlug(slug);

      if (!page) {
        return ctx.notFound('Page not found');
      }

      return ctx.send({ data: page });
    } catch (err) {
      console.error('Error in findBySlug page controller:', err);
      return ctx.badRequest('Failed to fetch page', { error: err.message });
    }
  },

  async findMyPages(ctx) {
    try {
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized('You must be logged in to view your pages');
      }

      const { pages, pagination } = await strapi.service('api::page.page').getUserPages(
        user.id,
        ctx.query.filters || {},
        {
          page: ctx.query.page || 1,
          pageSize: ctx.query.pageSize || 10,
        }
      );

      return ctx.send({ data: pages, meta: { pagination } });
    } catch (err) {
      console.error('Error in findMyPages controller:', err);
      return ctx.badRequest('Failed to fetch pages', { error: err.message });
    }
  },

  async findAvailableForReferrerLink(ctx) {
    try {
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized('You must be logged in to view available pages');
      }

      // Get pages that belong to the user and are NOT linked to any referrer-link
      const pages = await strapi.documents('api::page.page').findMany({
        filters: {
          author: user.id,
          status: 'published', // Only published pages can be linked
          referrer_link: {
            // @ts-expect-error - $null operator is valid in Strapi but not in type definitions
            $null: true // Pages that don't have a referrer_link relationship
          }
        },
        fields: ['id', 'title', 'slug'],
        sort: { createdAt: 'desc' },
      });

      return ctx.send({
        data: pages,
        meta: {
          total: pages.length
        }
      });
    } catch (error) {
      console.error('Error fetching available pages for referrer link:', error);
      return ctx.internalServerError('Error fetching available pages');
    }
  },

  async trackView(ctx) {
    try {
      const { id } = ctx.params;
      const { referrer, source_type = 'direct' } = ctx.request.body;

      if (!id) {
        return ctx.badRequest('Page ID is required');
      }

      // Validate source_type
      const validSourceTypes = ['direct', 'referrer_link', 'short_link'];
      if (!validSourceTypes.includes(source_type)) {
        return ctx.badRequest('Invalid source_type. Must be one of: direct, referrer_link, short_link');
      }

      // Find the page
      const page = await strapi.documents('api::page.page').findOne({
        documentId: id,
        populate: ['referrer_link']
      });

      if (!page) {
        return ctx.notFound('Page not found');
      }

      // Increment page view count
      await strapi.documents('api::page.page').update({
        documentId: id,
        data: {
          view_count: (page.view_count || 0) + 1
        }
      });

      // If page has a linked referrer-link, update its view tracking
      if (page.referrer_link) {
        const referrerLink = page.referrer_link;
        const updateData: any = {
          total_views: (referrerLink.total_views || 0) + 1
        };

        // Update specific view type counter
        switch (source_type) {
          case 'direct':
            updateData.direct_page_views = (referrerLink.direct_page_views || 0) + 1;
            break;
          case 'referrer_link':
            updateData.referrer_link_views = (referrerLink.referrer_link_views || 0) + 1;
            break;
          case 'short_link':
            updateData.short_link_views = (referrerLink.short_link_views || 0) + 1;
            break;
        }

        // Update referrer sources if provided
        if (referrer) {
          const referrerSources = referrerLink.referrer_sources || {};
          referrerSources[referrer] = (referrerSources[referrer] || 0) + 1;
          updateData.referrer_sources = referrerSources;
        }

        // Update the referrer-link
        await strapi.documents('api::referrer-link.referrer-link').update({
          documentId: referrerLink.documentId,
          data: updateData
        });
      }

      return ctx.send({
        success: true,
        message: 'View tracked successfully',
        data: {
          pageId: id,
          sourceType: source_type,
          referrer: referrer || null
        }
      });
    } catch (error) {
      console.error('Error tracking page view:', error);
      return ctx.internalServerError('Error tracking page view');
    }
  },

  async uploadImage(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to upload images');
      }

      // Get the uploaded files from the request
      const { files } = ctx.request;
      if (!files || !files.image) {
        return ctx.badRequest('No image file provided');
      }

      const imageFile = Array.isArray(files.image) ? files.image[0] : files.image;

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(imageFile.mimetype)) {
        return ctx.badRequest('Invalid file type. Only JPG, PNG, GIF, and WebP images are allowed.');
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (imageFile.size > maxSize) {
        return ctx.badRequest('File size too large. Maximum size is 5MB.');
      }

      // Get pageId from request body or query
      const pageId = ctx.request.body?.pageId || ctx.query.pageId;
      if (!pageId) {
        return ctx.badRequest('Page ID is required');
      }

      // Verify user has access to this page
      const page = await strapi.documents('api::page.page').findOne({
        documentId: pageId,
        populate: ['author'],
      });

      if (!page || page.author.id !== user.id) {
        return ctx.forbidden('You do not have permission to upload images for this page');
      }

      // Import S3 upload service (singleton instance)
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const s3Service = require('../../../utils/s3-upload').default;

      // Generate unique filename with timestamp
      const timestamp = Date.now();
      // Extract original filename from the uploaded file (Strapi/Koa file structure)
      const originalName = (imageFile as any).originalFilename || (imageFile as any).name || 'image.jpg';
      const fileExtension = originalName.split('.').pop() || 'jpg';
      const baseName = originalName.replace(/\.[^/.]+$/, ''); // Remove extension
      const fileName = `${timestamp}-${baseName}.${fileExtension}`;

      // Create folder path: user-pages/{userId}/{pageId}
      const folderPath = `user-pages/${user.id}/${pageId}`;

      // Upload to S3 using the file path (Strapi/Koa file structure)
      const filePath = (imageFile as any).filepath || (imageFile as any).path;
      const uploadResult = await s3Service.uploadImage(
        filePath,
        fileName,
        folderPath
      );

      console.log('Image uploaded successfully:', {
        userId: user.id,
        pageId,
        fileName,
        s3Url: uploadResult.publicUrl,
      });

      return ctx.send({
        s3Url: uploadResult.publicUrl,
        fileName: uploadResult.fileName,
        fileKey: uploadResult.fileKey,
      });

    } catch (err) {
      console.error('Error in uploadImage controller:', err);
      return ctx.badRequest('Failed to upload image', { error: err.message });
    }
  },
}));
